<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <?php echo $__env->make('kitchen.partials.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
            <!-- Header -->
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-4 border-bottom">
                <div>
                    <h1 class="h2 mb-0">
                        <i class="bi bi-clipboard-check text-primary me-2"></i>
                        Pre-Orders Management
                    </h1>
                    <p class="text-muted mb-0">Create and manage meal polls for students</p>
                </div>
                <div class="btn-toolbar">
                    <button type="button" class="btn btn-outline-secondary" onclick="loadPolls()">
                        <i class="bi bi-arrow-clockwise"></i> Refresh
                    </button>
                </div>
            </div>

            <!-- Alerts -->
            <?php if(session('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i><?php echo e(session('success')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i><?php echo e(session('error')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if($errors->any()): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>Please fix the following errors:</strong>
                    <ul class="mb-0 mt-2">
                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><?php echo e($error); ?></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Create Poll Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-plus-circle me-2"></i>Create New Menu Poll
                    </h5>
                </div>
                <div class="card-body">
                    <form id="createPollForm" class="needs-validation" novalidate>
                        <?php echo csrf_field(); ?>
                        <div class="row g-3 mb-3">
                            <div class="col-md-3">
                                <label for="pollMealType" class="form-label">Meal Type</label>
                                <select class="form-select" id="pollMealType" name="meal_type" required>
                                    <option value="">Select Meal Type</option>
                                    <option value="breakfast">Breakfast</option>
                                    <option value="lunch">Lunch</option>
                                    <option value="dinner">Dinner</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="pollDate" class="form-label">Poll Date</label>
                                <select class="form-select" id="pollDate" name="poll_date" required>
                                    <option value="today">Today</option>
                                    <option value="tomorrow">Tomorrow</option>
                                    <option value="custom">Custom Date...</option>
                                </select>
                                <input type="date" class="form-control mt-2 d-none" id="customPollDate" name="custom_poll_date">
                            </div>
                            <div class="col-md-3">
                                <label for="manualMealName" class="form-label">Meal Name *</label>
                                <input type="text" class="form-control" id="manualMealName" placeholder="e.g., Chicken Adobo" required>
                            </div>
                            <div class="col-md-3">
                                <label for="manualMealIngredients" class="form-label">Ingredients</label>
                                <input type="text" class="form-control" id="manualMealIngredients" placeholder="e.g., Chicken, soy sauce, vinegar">
                            </div>
                        </div>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="pollDeadlineTime" class="form-label">Response Deadline</label>
                                <select class="form-select" id="pollDeadlineTime" name="deadline_time">
                                    <option value="9:00 AM">9:00 AM</option>
                                    <option value="10:00 AM">10:00 AM</option>
                                    <option value="11:00 AM">11:00 AM</option>
                                    <option value="12:00 PM">12:00 PM (Noon)</option>
                                    <option value="1:00 PM">1:00 PM</option>
                                    <option value="2:00 PM">2:00 PM</option>
                                    <option value="3:00 PM">3:00 PM</option>
                                    <option value="4:00 PM">4:00 PM</option>
                                    <option value="5:00 PM">5:00 PM</option>
                                    <option value="6:00 PM">6:00 PM</option>
                                    <option value="7:00 PM">7:00 PM</option>
                                    <option value="8:00 PM">8:00 PM</option>
                                    <option value="9:00 PM" selected>9:00 PM</option>
                                    <option value="10:00 PM">10:00 PM</option>
                                    <option value="11:00 PM">11:00 PM</option>
                                    <option value="custom">Custom Time</option>
                                </select>
                                <input type="time" class="form-control mt-2 d-none" id="customDeadline" name="custom_deadline">
                            </div>
                            <div class="col-md-6 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100" id="createPollBtn">
                                    <i class="bi bi-plus"></i> Create Poll
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Filters Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-funnel me-2"></i>Filter Polls
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="dateFilter" class="form-label">Filter by Date</label>
                            <select class="form-select" id="dateFilter">
                                <option value="">All Dates</option>
                                <option value="<?php echo e(date('Y-m-d')); ?>">Today</option>
                                <option value="<?php echo e(date('Y-m-d', strtotime('+1 day'))); ?>">Tomorrow</option>
                                <option value="custom">Custom Date...</option>
                            </select>
                            <input type="date" class="form-control mt-2 d-none" id="customDateFilter">
                        </div>
                        <div class="col-md-4">
                            <label for="mealTypeFilter" class="form-label">Filter by Meal Type</label>
                            <select class="form-select" id="mealTypeFilter">
                                <option value="">All Meal Types</option>
                                <option value="breakfast">Breakfast</option>
                                <option value="lunch">Lunch</option>
                                <option value="dinner">Dinner</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="urgencyFilter" class="form-label">Filter by Urgency</label>
                            <select class="form-select" id="urgencyFilter">
                                <option value="">All Polls</option>
                                <option value="urgent">🔴 Urgent</option>
                                <option value="soon">🟡 Soon</option>
                                <option value="normal">🟢 Normal</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Polls Table Card -->
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-list-ul me-2"></i>Menu Polls
                    </h6>
                    <button type="button" class="btn btn-success btn-sm" onclick="sendAllActivePolls()">
                        <i class="bi bi-send me-1"></i> Send All Polls
                    </button>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Meal Details</th>
                                    <th>Date & Type</th>
                                    <th>Status</th>
                                    <th>Responses</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="preOrdersTableBody">
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="text-muted mt-2 mb-0">Loading polls...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Poll Deadline Modal -->
<div class="modal fade" id="editPollDeadlineModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-clock-history me-2"></i>Edit Poll Deadline
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editPollDeadlineForm">
                    <input type="hidden" id="editPollId" name="poll_id">
                    
                    <div class="mb-3">
                        <label class="form-label">Poll Information</label>
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 id="editPollMealName" class="card-title mb-1">-</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">
                                            <i class="bi bi-calendar"></i> <span id="editPollDate">-</span>
                                        </small>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">
                                            <i class="bi bi-clock"></i> <span id="editPollMealType">-</span>
                                        </small>
                                    </div>
                                </div>
                                <small id="editPollIngredients" class="text-muted d-block mt-1">-</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label for="editDeadlineDate" class="form-label">Deadline Date</label>
                            <select class="form-select" id="editDeadlineDate" name="deadline_date">
                                <option value="<?php echo e(date('Y-m-d')); ?>">Today</option>
                                <option value="<?php echo e(date('Y-m-d', strtotime('+1 day'))); ?>">Tomorrow</option>
                                <option value="custom">Custom Date...</option>
                            </select>
                            <input type="date" class="form-control mt-2 d-none" id="editCustomDate" name="custom_date">
                        </div>
                        <div class="col-md-6">
                            <label for="editDeadlineTime" class="form-label">Deadline Time</label>
                            <select class="form-select" id="editDeadlineTime" name="deadline_time">
                                <option value="9:00 AM">9:00 AM</option>
                                <option value="10:00 AM">10:00 AM</option>
                                <option value="11:00 AM">11:00 AM</option>
                                <option value="12:00 PM">12:00 PM</option>
                                <option value="1:00 PM">1:00 PM</option>
                                <option value="2:00 PM">2:00 PM</option>
                                <option value="3:00 PM">3:00 PM</option>
                                <option value="4:00 PM">4:00 PM</option>
                                <option value="5:00 PM">5:00 PM</option>
                                <option value="6:00 PM">6:00 PM</option>
                                <option value="7:00 PM">7:00 PM</option>
                                <option value="8:00 PM">8:00 PM</option>
                                <option value="9:00 PM">9:00 PM</option>
                                <option value="10:00 PM">10:00 PM</option>
                                <option value="11:00 PM">11:00 PM</option>
                                <option value="custom">Custom Time</option>
                            </select>
                            <input type="time" class="form-control mt-2 d-none" id="editCustomDeadlineTime" name="custom_deadline">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="savePollDeadlineBtn">
                    <i class="bi bi-check-circle"></i> Update Deadline
                </button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<style>
/* Modern UI Styles */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    padding: 1rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #e1e5e9;
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #22bbea;
    box-shadow: 0 0 0 0.2rem rgba(34, 187, 234, 0.15);
    outline: none;
}

.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #22bbea 0%, #1a9bd1 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1a9bd1 0%, #1587b8 100%);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20a039 100%);
    border: none;
}

.btn-outline-secondary {
    border-color: #e1e5e9;
    color: #6c757d;
}

.table {
    border-radius: 8px;
    overflow: hidden;
}

.table th {
    background-color: #f8f9fa;
    border: none;
    font-weight: 600;
    color: #495057;
    padding: 1rem;
}

.table td {
    border: none;
    padding: 1rem;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(34, 187, 234, 0.05);
}

.badge {
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    font-weight: 500;
}

.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-radius: 12px 12px 0 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.alert {
    border-radius: 8px;
    border: none;
    padding: 1rem 1.25rem;
}

.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* Status badges */
.badge.bg-success { background: linear-gradient(135deg, #28a745, #20a039) !important; }
.badge.bg-danger { background: linear-gradient(135deg, #dc3545, #c82333) !important; }
.badge.bg-warning { background: linear-gradient(135deg, #ffc107, #e0a800) !important; }
.badge.bg-info { background: linear-gradient(135deg, #17a2b8, #138496) !important; }
.badge.bg-secondary { background: linear-gradient(135deg, #6c757d, #5a6268) !important; }

/* Responsive improvements */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .table th, .table td {
        padding: 0.75rem 0.5rem;
        font-size: 0.875rem;
    }
}
</style>

<?php $__env->startPush('scripts'); ?>
<script>
// Configuration
const CONFIG = {
    endpoints: {
        polls: '/kitchen/pre-orders/polls',
        createPoll: '/kitchen/pre-orders/create-poll',
        sendPoll: '/kitchen/pre-orders/send-poll',
        sendAllPolls: '/kitchen/pre-orders/send-all-polls',
        updateDeadline: '/kitchen/pre-orders/update-deadline',
        deletePoll: '/kitchen/pre-orders/delete-poll',
        pollResults: '/kitchen/pre-orders/poll-results'
    },
    selectors: {
        createForm: '#createPollForm',
        tableBody: '#preOrdersTableBody',
        createBtn: '#createPollBtn',
        modal: '#editPollDeadlineModal'
    }
};

// Utility functions
const Utils = {
    showToast(message, type = 'info') {
        const toastHtml = `
            <div class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;

        let container = document.querySelector('.toast-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            document.body.appendChild(container);
        }

        container.insertAdjacentHTML('beforeend', toastHtml);
        const toast = new bootstrap.Toast(container.lastElementChild);
        toast.show();
    },

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('en-US', {
            weekday: 'short',
            month: 'short',
            day: 'numeric'
        });
    },

    formatTime(timeString) {
        if (!timeString) return 'Not set';

        // Handle different time formats
        if (timeString.includes('AM') || timeString.includes('PM')) {
            return timeString;
        }

        // Convert 24-hour to 12-hour format
        const [hours, minutes] = timeString.split(':');
        const hour = parseInt(hours);
        const ampm = hour >= 12 ? 'PM' : 'AM';
        const displayHour = hour % 12 || 12;
        return `${displayHour}:${minutes} ${ampm}`;
    },

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
};

// Poll management
const PollManager = {
    async loadPolls() {
        try {
            const response = await fetch(CONFIG.endpoints.polls);
            const data = await response.json();

            if (data.success) {
                this.renderPolls(data.polls || []);
            } else {
                throw new Error(data.message || 'Failed to load polls');
            }
        } catch (error) {
            console.error('Error loading polls:', error);
            Utils.showToast('Error loading polls: ' + error.message, 'error');
        }
    },

    renderPolls(polls) {
        const tbody = document.querySelector(CONFIG.selectors.tableBody);

        if (!polls.length) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center py-5">
                        <i class="bi bi-inbox display-4 text-muted"></i>
                        <p class="text-muted mt-3 mb-0">No polls found. Create your first poll above.</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = polls.map(poll => this.renderPollRow(poll)).join('');
    },

    renderPollRow(poll) {
        const statusBadge = this.getStatusBadge(poll.status);
        const urgencyBadge = this.getUrgencyBadge(poll);
        const responseCount = poll.responses_count || 0;
        const totalStudents = poll.total_students || 0;

        return `
            <tr>
                <td>
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">${Utils.escapeHtml(poll.meal_name || 'Unknown Meal')}</h6>
                            <small class="text-muted">${Utils.escapeHtml(poll.ingredients || 'No ingredients listed')}</small>
                            <div class="mt-1">
                                <small class="text-info">
                                    <i class="bi bi-clock"></i> Deadline: ${Utils.formatTime(poll.deadline)}
                                </small>
                            </div>
                        </div>
                        ${urgencyBadge}
                    </div>
                </td>
                <td>
                    <div class="text-center">
                        <div class="fw-bold">${Utils.formatDate(poll.poll_date)}</div>
                        <small class="text-muted text-capitalize">${poll.meal_type || 'Unknown'}</small>
                    </div>
                </td>
                <td class="text-center">${statusBadge}</td>
                <td class="text-center">
                    <div class="fw-bold text-primary">${responseCount}/${totalStudents}</div>
                    <small class="text-muted">responses</small>
                </td>
                <td class="text-center">
                    ${this.getActionButtons(poll)}
                </td>
            </tr>
        `;
    },

    getStatusBadge(status) {
        const badges = {
            draft: '<span class="badge bg-secondary">Draft</span>',
            active: '<span class="badge bg-success">Active</span>',
            sent: '<span class="badge bg-info">Sent</span>',
            closed: '<span class="badge bg-dark">Closed</span>'
        };
        return badges[status] || '<span class="badge bg-warning">Unknown</span>';
    },

    getUrgencyBadge(poll) {
        // Calculate urgency based on deadline
        if (!poll.deadline || !poll.poll_date) return '';

        try {
            const now = new Date();
            const deadline = new Date(`${poll.poll_date} ${poll.deadline}`);
            const hoursLeft = (deadline - now) / (1000 * 60 * 60);

            if (hoursLeft <= 0) {
                return '<span class="badge bg-danger">🔴 EXPIRED</span>';
            } else if (hoursLeft <= 2) {
                return '<span class="badge bg-danger">🚨 URGENT</span>';
            } else if (hoursLeft <= 6) {
                return '<span class="badge bg-warning">⚠️ SOON</span>';
            }
        } catch (error) {
            console.error('Error calculating urgency:', error);
        }

        return '';
    },

    getActionButtons(poll) {
        const baseClass = 'btn btn-sm me-1 mb-1';

        switch (poll.status) {
            case 'draft':
                return `
                    <button class="${baseClass} btn-success" onclick="PollManager.sendPoll('${poll.id}')">
                        <i class="bi bi-send"></i> Send
                    </button>
                    <button class="${baseClass} btn-outline-primary" onclick="PollManager.editDeadline('${poll.id}')">
                        <i class="bi bi-clock"></i> Edit
                    </button>
                    <button class="${baseClass} btn-outline-danger" onclick="PollManager.deletePoll('${poll.id}')">
                        <i class="bi bi-trash"></i> Delete
                    </button>
                `;
            case 'active':
            case 'sent':
                return `
                    <button class="${baseClass} btn-info" onclick="PollManager.viewResults('${poll.id}')">
                        <i class="bi bi-bar-chart"></i> Results
                    </button>
                    <button class="${baseClass} btn-outline-primary" onclick="PollManager.editDeadline('${poll.id}')">
                        <i class="bi bi-clock"></i> Edit
                    </button>
                    <button class="${baseClass} btn-outline-danger" onclick="PollManager.deletePoll('${poll.id}')">
                        <i class="bi bi-trash"></i> Delete
                    </button>
                `;
            default:
                return `<span class="text-muted">No actions</span>`;
        }
    },

    async sendPoll(pollId) {
        if (!confirm('Send this poll to students?')) return;

        try {
            const response = await fetch(CONFIG.endpoints.sendPoll, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({ poll_id: pollId })
            });

            const data = await response.json();

            if (data.success) {
                Utils.showToast('Poll sent successfully!', 'success');
                this.loadPolls();
            } else {
                throw new Error(data.message || 'Failed to send poll');
            }
        } catch (error) {
            Utils.showToast('Error sending poll: ' + error.message, 'error');
        }
    },

    async deletePoll(pollId) {
        if (!confirm('Are you sure you want to delete this poll?')) return;

        try {
            const response = await fetch(`${CONFIG.endpoints.deletePoll}/${pollId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            const data = await response.json();

            if (data.success) {
                Utils.showToast('Poll deleted successfully!', 'success');
                this.loadPolls();
            } else {
                throw new Error(data.message || 'Failed to delete poll');
            }
        } catch (error) {
            Utils.showToast('Error deleting poll: ' + error.message, 'error');
        }
    },

    editDeadline(pollId) {
        // Implementation for editing deadline
        Utils.showToast('Edit deadline functionality coming soon!', 'info');
    },

    viewResults(pollId) {
        // Implementation for viewing results
        Utils.showToast('View results functionality coming soon!', 'info');
    }
};

// Form handling
const FormHandler = {
    init() {
        const form = document.querySelector(CONFIG.selectors.createForm);
        if (form) {
            form.addEventListener('submit', this.handleSubmit.bind(this));
        }

        // Handle custom date/time toggles
        this.setupCustomToggles();
    },

    setupCustomToggles() {
        // Poll date custom toggle
        const pollDate = document.getElementById('pollDate');
        const customPollDate = document.getElementById('customPollDate');

        if (pollDate && customPollDate) {
            pollDate.addEventListener('change', function() {
                if (this.value === 'custom') {
                    customPollDate.classList.remove('d-none');
                    customPollDate.required = true;
                } else {
                    customPollDate.classList.add('d-none');
                    customPollDate.required = false;
                    customPollDate.value = '';
                }
            });
        }

        // Deadline time custom toggle
        const deadlineTime = document.getElementById('pollDeadlineTime');
        const customDeadline = document.getElementById('customDeadline');

        if (deadlineTime && customDeadline) {
            deadlineTime.addEventListener('change', function() {
                if (this.value === 'custom') {
                    customDeadline.classList.remove('d-none');
                    customDeadline.required = true;
                } else {
                    customDeadline.classList.add('d-none');
                    customDeadline.required = false;
                    customDeadline.value = '';
                }
            });
        }
    },

    async handleSubmit(e) {
        e.preventDefault();

        const form = e.target;
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        // Add meal name and ingredients from manual inputs
        data.meal_name = document.getElementById('manualMealName').value;
        data.ingredients = document.getElementById('manualMealIngredients').value;

        try {
            const response = await fetch(CONFIG.endpoints.createPoll, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                Utils.showToast('Poll created successfully!', 'success');
                form.reset();
                this.resetCustomFields();
                PollManager.loadPolls();
            } else {
                throw new Error(result.message || 'Failed to create poll');
            }
        } catch (error) {
            Utils.showToast('Error creating poll: ' + error.message, 'error');
        }
    },

    resetCustomFields() {
        document.getElementById('customPollDate').classList.add('d-none');
        document.getElementById('customDeadline').classList.add('d-none');
    }
};

// Global functions for backward compatibility
window.loadPolls = () => PollManager.loadPolls();
window.sendAllActivePolls = async () => {
    if (!confirm('Send all draft polls to students?')) return;

    try {
        const response = await fetch(CONFIG.endpoints.sendAllPolls, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        const data = await response.json();

        if (data.success) {
            Utils.showToast(`${data.count || 0} polls sent successfully!`, 'success');
            PollManager.loadPolls();
        } else {
            throw new Error(data.message || 'Failed to send polls');
        }
    } catch (error) {
        Utils.showToast('Error sending polls: ' + error.message, 'error');
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    FormHandler.init();
    PollManager.loadPolls();

    // Auto-refresh every 2 minutes
    setInterval(() => PollManager.loadPolls(), 120000);
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\setup\Capstone\Github\Capstone14\capstone\resources\views/kitchen/pre-orders.blade.php ENDPATH**/ ?>